const NotificationSetting = require('../../models/NotificationSetting');
const { processAbandonedCarts, startAbandonedCartCronJob, getCronJobStatus } = require('../../services/abandonedCartService');
const ScrapingService = require('../../services/scrapingService');

/**
 * Get all notification settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getNotificationSettings = async (req, res) => {
  try {
    const settings = await NotificationSetting.find();

    // If no settings exist yet, return default settings
    if (settings.length === 0) {
      return res.status(200).json({
        success: true,
        data: [{
          type: 'abandoned_cart',
          enabled: true,
          timeThreshold: 86400, // 24 hours in seconds
          cronSchedule: '0 0 * * *', // Daily at midnight
          emailEnabled: true,
          inAppEnabled: true,
          notificationTitle: 'Your Cart is Waiting',
          notificationMessage: 'You have {{cartCount}} item(s) in your cart. Complete your purchase!',
          isDefault: true // Flag to indicate this is a default setting not saved in DB
        }]
      });
    }

    return res.status(200).json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('[NOTIFICATION SETTINGS] Error getting notification settings:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get notification settings',
      error: error.message
    });
  }
};

/**
 * Get a specific notification setting by type
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getNotificationSettingByType = async (req, res) => {
  try {
    const { type } = req.params;

    let setting = await NotificationSetting.findOne({ type });

    // If setting doesn't exist, return default
    if (!setting) {
      if (type === 'abandoned_cart') {
        return res.status(200).json({
          success: true,
          data: {
            type: 'abandoned_cart',
            enabled: true,
            timeThreshold: 86400, // 24 hours in seconds
            cronSchedule: '0 0 * * *', // Daily at midnight
            emailEnabled: true,
            inAppEnabled: true,
            notificationTitle: 'Your Cart is Waiting',
            notificationMessage: 'You have {{cartCount}} item(s) in your cart. Complete your purchase!',
            isDefault: true // Flag to indicate this is a default setting not saved in DB
          }
        });
      } else if (type === 'vps_scraping') {
        return res.status(200).json({
          success: true,
          data: {
            type: 'vps_scraping',
            enabled: true,
            timeThreshold: 0, // Not applicable for VPS scraping
            cronSchedule: '0 2 * * *', // Daily at 2 AM UTC
            emailEnabled: true, // Send emails to all admin users
            inAppEnabled: true,
            notificationTitle: 'VPS Pricing Update',
            notificationMessage: 'Contabo VPS pricing has been updated automatically.',
            pricingMargin: 0, // 0% margin by default
            isDefault: true
          }
        });
      } else {
        return res.status(404).json({
          success: false,
          message: `Notification setting with type ${type} not found`
        });
      }
    }

    return res.status(200).json({
      success: true,
      data: setting
    });
  } catch (error) {
    console.error(`[NOTIFICATION SETTINGS] Error getting notification setting for type ${req.params.type}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get notification setting',
      error: error.message
    });
  }
};

/**
 * Update a notification setting
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateNotificationSetting = async (req, res) => {
  try {
    const { type } = req.params;
    const updateData = req.body;

    // Add updatedBy if user is authenticated
    if (req.user && req.user._id) {
      updateData.updatedBy = req.user._id;
    }

    // Update or create the setting
    let setting = await NotificationSetting.findOne({ type });

    if (setting) {
      // Update existing setting
      setting = await NotificationSetting.findOneAndUpdate(
        { type },
        updateData,
        { new: true, runValidators: true }
      );
    } else {
      // Create new setting
      setting = await NotificationSetting.create({
        type,
        ...updateData
      });
    }

    // If this is an abandoned cart setting update, restart the cron job
    if (type === 'abandoned_cart') {
      try {
        console.log('[NOTIFICATION SETTINGS] Restarting abandoned cart cron job due to settings update');
        await startAbandonedCartCronJob();
        console.log('[NOTIFICATION SETTINGS] Abandoned cart cron job restarted successfully');
      } catch (cronError) {
        console.error('[NOTIFICATION SETTINGS] Error restarting abandoned cart cron job:', cronError);
        // Don't fail the request if cron restart fails, just log it
      }
    }

    // If this is a VPS scraping setting update, update the scheduled tasks
    if (type === 'vps_scraping') {
      try {
        console.log('[NOTIFICATION SETTINGS] Updating VPS scraping schedule due to settings update');
        const ScrapingService = require('../../services/scrapingService');
        const scrapingService = new ScrapingService();

        if (setting.enabled) {
          // Sync the new schedule to database tasks
          await scrapingService.syncScheduleFromSettings();
          console.log('[NOTIFICATION SETTINGS] VPS scraping schedule updated successfully');
        } else {
          // Cancel all scheduled tasks if disabled
          const VpsScheduledTask = require('../../models/VpsScheduledTask');
          await VpsScheduledTask.cancelAllScheduledTasks();
          console.log('[NOTIFICATION SETTINGS] VPS scraping disabled, cancelled all scheduled tasks');
        }
      } catch (cronError) {
        console.error('[NOTIFICATION SETTINGS] Error updating VPS scraping schedule:', cronError);
        // Don't fail the request if schedule update fails, just log it
      }
    }

    return res.status(200).json({
      success: true,
      data: setting,
      message: 'Notification setting updated successfully'
    });
  } catch (error) {
    console.error(`[NOTIFICATION SETTINGS] Error updating notification setting for type ${req.params.type}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update notification setting',
      error: error.message
    });
  }
};

/**
 * Manually trigger a notification process
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const triggerNotification = async (req, res) => {
  try {
    const { type } = req.params;

    if (type === 'abandoned_cart') {
      // Log current cron job status
      const cronStatus = getCronJobStatus();
      console.log(`[NOTIFICATION SETTINGS] Current cron job status:`, cronStatus);

      // Manually trigger the process
      console.log(`[NOTIFICATION SETTINGS] Manually triggering abandoned cart notification process`);
      await processAbandonedCarts();

      return res.status(200).json({
        success: true,
        message: 'Abandoned cart notification process triggered successfully',
        cronJobStatus: cronStatus
      });
    } else if (type === 'vps_scraping') {
      // Manually trigger VPS scraping
      console.log(`[NOTIFICATION SETTINGS] Manually triggering VPS scraping process`);

      const scrapingService = new ScrapingService();
      const result = await scrapingService.runScrapingPipeline(false); // false = manual run

      return res.status(200).json({
        success: true,
        message: 'VPS scraping process triggered successfully',
        result: {
          success: result.success,
          scraped: result.scraped || 0,
          created: result.created || 0,
          updated: result.updated || 0,
          errors: result.errors || 0,
          timestamp: result.timestamp
        }
      });
    } else {
      return res.status(400).json({
        success: false,
        message: `Notification type ${type} cannot be manually triggered`
      });
    }
  } catch (error) {
    console.error(`[NOTIFICATION SETTINGS] Error triggering notification for type ${req.params.type}:`, error);
    return res.status(500).json({
      success: false,
      message: 'Failed to trigger notification process',
      error: error.message
    });
  }
};

module.exports = {
  getNotificationSettings,
  getNotificationSettingByType,
  updateNotificationSetting,
  triggerNotification
};
