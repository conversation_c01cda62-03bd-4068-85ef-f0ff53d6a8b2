/**
 * VPS Controller
 * Handles HTTP requests for VPS operations
 * Follows Single Responsibility Principle and Dependency Inversion Principle
 */

const VPSService = require('../services/vpsService');
const VPSProviderFactory = require('../services/providers/VPSProviderFactory');

class VPSController {
  constructor() {
    this.vpsService = new VPSService();
  }

  /**
   * Get available VPS plans
   * GET /vps/plans
   */
  async getPlans(req, res) {
    try {
      const { provider } = req.query;
      const plans = await this.vpsService.getAvailablePlans(provider);
      
      res.status(200).json({
        success: true,
        message: req.t('vps.plans_retrieved_successfully'),
        data: plans,
        meta: {
          count: plans.length,
          providers: VPSProviderFactory.getSupportedProviders()
        }
      });
    } catch (error) {
      console.error('Error fetching VPS plans:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_plans'),
        error: error.message
      });
    }
  }

  /**
   * Get VPS plan details
   * GET /vps/plans/:planId
   */
  async getPlanDetails(req, res) {
    try {
      const { planId } = req.params;
      const { provider } = req.query;
      
      const planDetails = await this.vpsService.getPlanDetails(planId, provider);
      
      res.status(200).json({
        success: true,
        message: req.t('vps.plan_details_retrieved'),
        data: planDetails
      });
    } catch (error) {
      console.error('Error fetching VPS plan details:', error.message);
      res.status(404).json({
        success: false,
        message: req.t('vps.plan_not_found'),
        error: error.message
      });
    }
  }

  /**
   * Create VPS order
   * POST /vps/order
   */
  async createOrder(req, res) {
    try {
      const user = req.user;
      const orderData = {
        ...req.body,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      };

      const order = await this.vpsService.createVPSOrder(orderData, user);

      res.status(201).json({
        success: true,
        message: req.t('vps.order_created_successfully'),
        data: order
      });
    } catch (error) {
      console.error('Error creating VPS order:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_create_order'),
        error: error.message
      });
    }
  }

  /**
   * Get user's VPS orders
   * GET /vps/orders
   */
  async getUserOrders(req, res) {
    try {
      const userId = req.user._id;
      const { status } = req.query;
      
      const orders = await this.vpsService.getUserVPSOrders(userId, status);
      
      res.status(200).json({
        success: true,
        message: req.t('vps.orders_retrieved_successfully'),
        data: orders,
        meta: {
          count: orders.length
        }
      });
    } catch (error) {
      console.error('Error fetching user VPS orders:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_orders'),
        error: error.message
      });
    }
  }

  /**
   * Get user's VPS instances directly from Contabo
   * GET /vps/instances
   */
  async getUserInstances(req, res) {
    try {
      // Pour les tests, utiliser un userId par défaut si pas d'utilisateur connecté
      const userId = req.user?._id;
      const { status } = req.query;

      console.log(`🔍 Fetching VPS instances for user: ${userId}`);

      // Get instances directly from Contabo API
      // const instances = await this.vpsService.getContaboVPSInstances(userId, status);
      const instances = await this.vpsService.getUserVPSInstances(userId, status);

      // Fallback to local database if Contabo API fails
      // const instances = await this.vpsService.getUserVPSInstances(userId, status);

      console.log(`✅ Found ${instances.length} VPS instances from Contabo`);

      res.status(200).json({
        success: true,
        message: req.t('vps.instances_retrieved_successfully'),
        data: instances,
        meta: {
          count: instances.length,
          source: 'contabo_api',
          userId: userId
        }
      });
    } catch (error) {
      console.error('❌ Error fetching VPS instances from Contabo:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_instances'),
        error: error.message
      });
    }
  }

  /**
   * Control VPS instance (start, stop, restart, reset-password)
   * POST /vps/instances/:instanceId/control
   */
  async controlInstance(req, res) {
    try {
      const { instanceId } = req.params;
      const { action } = req.body;
      const userId = req.user._id;

      if (!['start', 'stop', 'restart', 'reset-password'].includes(action)) {
        return res.status(400).json({
          success: false,
          message: req.t('vps.invalid_action')
        });
      }

      const result = await this.vpsService.controlVPSInstance(instanceId, action, userId);

      res.status(200).json({
        success: true,
        message: req.t(`vps.instance_${action.replace('-', '_')}_success`),
        data: result
      });
    } catch (error) {
      console.error('Error controlling VPS instance:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.control_action_failed'),
        error: error.message
      });
    }
  }

  /**
   * Create VPS snapshot
   * POST /vps/instances/:instanceId/snapshot
   */
  async createSnapshot(req, res) {
    try {
      const { instanceId } = req.params;
      const { snapshotName } = req.body;
      const userId = req.user._id;

      const VPSInstance = require('../models/VPSInstance');
      const instance = await VPSInstance.findOne({
        instanceId,
        user: userId
      });

      if (!instance) {
        return res.status(404).json({
          success: false,
          message: req.t('vps.instance_not_found')
        });
      }

      const provider = this.vpsService.getProvider(instance.config.provider);
      const result = await provider.createSnapshot(instanceId, snapshotName);

      res.status(200).json({
        success: true,
        message: req.t('vps.snapshot_created_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error creating VPS snapshot:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.snapshot_creation_failed'),
        error: error.message
      });
    }
  }

  /**
   * Reinstall VPS instance
   * POST /vps/instances/:instanceId/reinstall
   */
  async reinstallInstance(req, res) {
    try {
      const { instanceId } = req.params;
      const { imageId } = req.body;
      const userId = req.user._id;

      const VPSInstance = require('../models/VPSInstance');
      const instance = await VPSInstance.findOne({
        instanceId,
        user: userId
      });

      if (!instance) {
        return res.status(404).json({
          success: false,
          message: req.t('vps.instance_not_found')
        });
      }

      const provider = this.vpsService.getProvider(instance.config.provider);
      const result = await provider.reinstallVPS(instanceId, imageId);

      // Update instance OS in database
      instance.config.operatingSystem = imageId;
      await instance.save();

      res.status(200).json({
        success: true,
        message: req.t('vps.instance_reinstalled_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error reinstalling VPS instance:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.reinstall_failed'),
        error: error.message
      });
    }
  }

  /**
   * Get available OS images
   * GET /vps/images
   */
  async getImages(req, res) {
    try {
      const { provider } = req.query;
      const providerInstance = this.vpsService.getProvider(provider);
      const images = await providerInstance.getImages();

      res.status(200).json({
        success: true,
        message: req.t('vps.images_retrieved_successfully'),
        data: images
      });
    } catch (error) {
      console.error('Error fetching VPS images:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_images'),
        error: error.message
      });
    }
  }

  /**
   * Get available regions
   * GET /vps/regions
   */
  async getRegions(req, res) {
    try {
      const { provider } = req.query;
      const providerInstance = this.vpsService.getProvider(provider);
      const regions = await providerInstance.getRegions();

      res.status(200).json({
        success: true,
        message: req.t('vps.regions_retrieved_successfully'),
        data: regions
      });
    } catch (error) {
      console.error('Error fetching VPS regions:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_regions'),
        error: error.message
      });
    }
  }

  /**
   * Get VPS instance details
   * GET /vps/instances/:instanceId
   */
  async getInstanceDetails(req, res) {
    try {
      const { instanceId } = req.params;
      const userId = req.user._id;
      
      const VPSInstance = require('../models/VPSInstance');
      const instance = await VPSInstance.findOne({ 
        instanceId, 
        user: userId 
      }).populate('order', 'orderId status pricing');
      
      if (!instance) {
        return res.status(404).json({
          success: false,
          message: req.t('vps.instance_not_found')
        });
      }
      
      res.status(200).json({
        success: true,
        message: req.t('vps.instance_details_retrieved'),
        data: instance
      });
    } catch (error) {
      console.error('Error fetching VPS instance details:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_instance_details'),
        error: error.message
      });
    }
  }

  /**
   * Process payment confirmation for VPS order
   * POST /vps/payment/confirm
   */
  async confirmPayment(req, res) {
    try {
      const { orderId, paymentId, transactionId } = req.body;

      const paymentData = {
        paymentId,
        transactionId
      };

      const order = await this.vpsService.processPaymentConfirmation(orderId, paymentData);

      res.status(200).json({
        success: true,
        message: req.t('vps.payment_confirmed_successfully'),
        data: order
      });
    } catch (error) {
      console.error('Error confirming VPS payment:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.payment_confirmation_failed'),
        error: error.message
      });
    }
  }

  /**
   * Get VPS suborder details
   * GET /vps/suborders/:subOrderId
   */
  async getSubOrderDetails(req, res) {
    try {
      const { subOrderId } = req.params;
      const userId = req.user._id;

      const SubOrder = require('../models/SubOrder');
      const subOrder = await SubOrder.findOne({
        _id: subOrderId,
        vps: { $ne: null }
      }).populate('package');

      if (!subOrder) {
        return res.status(404).json({
          success: false,
          message: req.t('vps.suborder_not_found')
        });
      }

      // Verify user owns this suborder through the main order
      const Order = require('../models/Order');
      const order = await Order.findOne({
        user: userId,
        subOrders: subOrderId
      });

      if (!order) {
        return res.status(403).json({
          success: false,
          message: req.t('vps.access_denied')
        });
      }

      res.status(200).json({
        success: true,
        message: req.t('vps.suborder_details_retrieved'),
        data: subOrder
      });
    } catch (error) {
      console.error('Error fetching VPS suborder details:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_suborder_details'),
        error: error.message
      });
    }
  }

  /**
   * Get supported providers
   * GET /vps/providers
   */
  async getProviders(req, res) {
    try {
      const providers = VPSProviderFactory.getSupportedProviders();
      const providerDetails = [];
      
      for (const provider of providers) {
        const capabilities = VPSProviderFactory.getProviderCapabilities(provider);
        const isConfigValid = await VPSProviderFactory.validateProviderConfig(provider);
        
        providerDetails.push({
          name: provider,
          capabilities,
          isConfigured: isConfigValid,
          status: isConfigValid ? 'active' : 'inactive'
        });
      }
      
      res.status(200).json({
        success: true,
        message: req.t('vps.providers_retrieved_successfully'),
        data: providerDetails
      });
    } catch (error) {
      console.error('Error fetching VPS providers:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_providers'),
        error: error.message
      });
    }
  }

  /**
   * Get VPS usage statistics
   * GET /vps/instances/:instanceId/stats
   */
  async getInstanceStats(req, res) {
    try {
      const { instanceId } = req.params;
      const userId = req.user._id;
      
      const VPSInstance = require('../models/VPSInstance');
      const instance = await VPSInstance.findOne({ 
        instanceId, 
        user: userId 
      });
      
      if (!instance) {
        return res.status(404).json({
          success: false,
          message: req.t('vps.instance_not_found')
        });
      }

      // Get stats from provider
      const provider = this.vpsService.getProvider(instance.config.provider);
      const stats = await provider.getVPSStats(instanceId);
      
      // Update instance usage data
      await instance.updateUsage(stats);
      
      res.status(200).json({
        success: true,
        message: req.t('vps.stats_retrieved_successfully'),
        data: stats
      });
    } catch (error) {
      console.error('Error fetching VPS stats:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_stats'),
        error: error.message
      });
    }
  }

  // ==================== User Management Methods ====================

  /**
   * Get provider users (Admin only)
   * GET /vps/users
   */
  async getProviderUsers(req, res) {
    try {
      const { provider, page, size, orderBy, email, enabled, owner } = req.query;

      const options = {
        ...(page && { page: parseInt(page) }),
        ...(size && { size: parseInt(size) }),
        ...(orderBy && { orderBy }),
        ...(email && { email }),
        ...(enabled !== undefined && { enabled: enabled === 'true' }),
        ...(owner !== undefined && { owner: owner === 'true' })
      };

      const users = await this.vpsService.getProviderUsers(provider, options);

      res.status(200).json({
        success: true,
        message: req.t('vps.users_retrieved_successfully'),
        data: users
      });
    } catch (error) {
      console.error('Error fetching provider users:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_users'),
        error: error.message
      });
    }
  }

  /**
   * Get provider user details (Admin only)
   * GET /vps/users/:userId
   */
  async getProviderUserDetails(req, res) {
    try {
      const { userId } = req.params;
      const { provider } = req.query;

      const user = await this.vpsService.getProviderUserDetails(userId, provider);

      res.status(200).json({
        success: true,
        message: req.t('vps.user_details_retrieved'),
        data: user
      });
    } catch (error) {
      console.error('Error fetching provider user details:', error.message);
      res.status(404).json({
        success: false,
        message: req.t('vps.user_not_found'),
        error: error.message
      });
    }
  }

  /**
   * Create provider user (Admin only)
   * POST /vps/users
   */
  async createProviderUser(req, res) {
    try {
      const { provider } = req.query;
      const userData = req.body;

      const user = await this.vpsService.createProviderUser(userData, provider);

      res.status(201).json({
        success: true,
        message: req.t('vps.user_created_successfully'),
        data: user
      });
    } catch (error) {
      console.error('Error creating provider user:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_create_user'),
        error: error.message
      });
    }
  }

  /**
   * Update provider user (Admin only)
   * PATCH /vps/users/:userId
   */
  async updateProviderUser(req, res) {
    try {
      const { userId } = req.params;
      const { provider } = req.query;
      const userData = req.body;

      const user = await this.vpsService.updateProviderUser(userId, userData, provider);

      res.status(200).json({
        success: true,
        message: req.t('vps.user_updated_successfully'),
        data: user
      });
    } catch (error) {
      console.error('Error updating provider user:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_update_user'),
        error: error.message
      });
    }
  }

  /**
   * Delete provider user (Admin only)
   * DELETE /vps/users/:userId
   */
  async deleteProviderUser(req, res) {
    try {
      const { userId } = req.params;
      const { provider } = req.query;

      const result = await this.vpsService.deleteProviderUser(userId, provider);

      res.status(200).json({
        success: true,
        message: req.t('vps.user_deleted_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error deleting provider user:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_delete_user'),
        error: error.message
      });
    }
  }

  /**
   * Generate client secret for user (Admin only)
   * PUT /vps/users/:userId/client-secret
   */
  async generateUserClientSecret(req, res) {
    try {
      const { userId } = req.params;
      const { provider } = req.query;

      const result = await this.vpsService.generateUserClientSecret(userId, provider);

      res.status(200).json({
        success: true,
        message: req.t('vps.client_secret_generated_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error generating client secret:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_generate_client_secret'),
        error: error.message
      });
    }
  }

  /**
   * Reset user password (Admin only)
   * POST /vps/users/:userId/reset-password
   */
  async resetProviderUserPassword(req, res) {
    try {
      const { userId } = req.params;
      const { provider } = req.query;
      const { redirectUrl } = req.body;

      const result = await this.vpsService.resetProviderUserPassword(userId, redirectUrl, provider);

      res.status(200).json({
        success: true,
        message: req.t('vps.password_reset_sent_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error resetting user password:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_reset_password'),
        error: error.message
      });
    }
  }

  // ==================== Data Centers Methods ====================

  /**
   * Get data centers
   * GET /vps/data-centers
   */
  async getDataCenters(req, res) {
    try {
      const { provider } = req.query;
      const providerInstance = this.vpsService.getProvider(provider);
      const dataCenters = await providerInstance.getDataCenters();

      res.status(200).json({
        success: true,
        message: req.t('vps.data_centers_retrieved_successfully'),
        data: dataCenters
      });
    } catch (error) {
      console.error('Error fetching data centers:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_data_centers'),
        error: error.message
      });
    }
  }

  /**
   * Get regions
   * GET /vps/regions
   */
  async getRegions(req, res) {
    try {
      const { provider } = req.query;
      const providerInstance = this.vpsService.getProvider(provider);
      const regions = await providerInstance.getRegions();

      res.status(200).json({
        success: true,
        message: req.t('vps.regions_retrieved_successfully'),
        data: regions
      });
    } catch (error) {
      console.error('Error fetching regions:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_regions'),
        error: error.message
      });
    }
  }

  // ==================== Object Storage Methods ====================

  /**
   * Get object storages
   * GET /vps/object-storages
   */
  async getObjectStorages(req, res) {
    try {
      const { provider, page, size, orderBy, dataCenter, s3TenantId } = req.query;

      const options = {
        ...(page && { page: parseInt(page) }),
        ...(size && { size: parseInt(size) }),
        ...(orderBy && { orderBy }),
        ...(dataCenter && { dataCenter }),
        ...(s3TenantId && { s3TenantId })
      };

      const providerInstance = this.vpsService.getProvider(provider);
      const objectStorages = await providerInstance.getObjectStorages(options);

      res.status(200).json({
        success: true,
        message: req.t('vps.object_storages_retrieved_successfully'),
        data: objectStorages
      });
    } catch (error) {
      console.error('Error fetching object storages:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_object_storages'),
        error: error.message
      });
    }
  }

  /**
   * Get object storage details
   * GET /vps/object-storages/:objectStorageId
   */
  async getObjectStorageDetails(req, res) {
    try {
      const { objectStorageId } = req.params;
      const { provider } = req.query;

      const providerInstance = this.vpsService.getProvider(provider);
      const objectStorage = await providerInstance.getObjectStorageDetails(objectStorageId);

      res.status(200).json({
        success: true,
        message: req.t('vps.object_storage_details_retrieved'),
        data: objectStorage
      });
    } catch (error) {
      console.error('Error fetching object storage details:', error.message);
      res.status(404).json({
        success: false,
        message: req.t('vps.object_storage_not_found'),
        error: error.message
      });
    }
  }

  /**
   * Create object storage
   * POST /vps/object-storages
   */
  async createObjectStorage(req, res) {
    try {
      const { provider } = req.query;
      const storageData = req.body;

      const providerInstance = this.vpsService.getProvider(provider);
      const objectStorage = await providerInstance.createObjectStorage(storageData);

      res.status(201).json({
        success: true,
        message: req.t('vps.object_storage_created_successfully'),
        data: objectStorage
      });
    } catch (error) {
      console.error('Error creating object storage:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_create_object_storage'),
        error: error.message
      });
    }
  }

  /**
   * Update object storage
   * PATCH /vps/object-storages/:objectStorageId
   */
  async updateObjectStorage(req, res) {
    try {
      const { objectStorageId } = req.params;
      const { provider } = req.query;
      const storageData = req.body;

      const providerInstance = this.vpsService.getProvider(provider);
      const objectStorage = await providerInstance.updateObjectStorage(objectStorageId, storageData);

      res.status(200).json({
        success: true,
        message: req.t('vps.object_storage_updated_successfully'),
        data: objectStorage
      });
    } catch (error) {
      console.error('Error updating object storage:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_update_object_storage'),
        error: error.message
      });
    }
  }

  /**
   * Cancel object storage
   * PATCH /vps/object-storages/:objectStorageId/cancel
   */
  async cancelObjectStorage(req, res) {
    try {
      const { objectStorageId } = req.params;
      const { provider } = req.query;

      const providerInstance = this.vpsService.getProvider(provider);
      const result = await providerInstance.cancelObjectStorage(objectStorageId);

      res.status(200).json({
        success: true,
        message: req.t('vps.object_storage_cancelled_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error cancelling object storage:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_cancel_object_storage'),
        error: error.message
      });
    }
  }

  /**
   * Resize object storage
   * POST /vps/object-storages/:objectStorageId/resize
   */
  async resizeObjectStorage(req, res) {
    try {
      const { objectStorageId } = req.params;
      const { provider } = req.query;
      const { totalPurchasedSpaceTB } = req.body;

      const providerInstance = this.vpsService.getProvider(provider);
      const result = await providerInstance.resizeObjectStorage(objectStorageId, totalPurchasedSpaceTB);

      res.status(200).json({
        success: true,
        message: req.t('vps.object_storage_resized_successfully'),
        data: result
      });
    } catch (error) {
      console.error('Error resizing object storage:', error.message);
      res.status(400).json({
        success: false,
        message: req.t('vps.failed_to_resize_object_storage'),
        error: error.message
      });
    }
  }

  /**
   * Get object storage statistics
   * GET /vps/object-storages/:objectStorageId/stats
   */
  async getObjectStorageStats(req, res) {
    try {
      const { objectStorageId } = req.params;
      const { provider } = req.query;

      const providerInstance = this.vpsService.getProvider(provider);
      const stats = await providerInstance.getObjectStorageStats(objectStorageId);

      res.status(200).json({
        success: true,
        message: req.t('vps.object_storage_stats_retrieved_successfully'),
        data: stats
      });
    } catch (error) {
      console.error('Error fetching object storage stats:', error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.failed_to_fetch_object_storage_stats'),
        error: error.message
      });
    }
  }

  // ==================== Placeholder Methods for Future Implementation ====================

  // Secrets Management
  async getSecrets(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async createSecret(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async updateSecret(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async deleteSecret(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }

  // Tags Management
  async getTags(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async createTag(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async updateTag(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async deleteTag(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async assignTag(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async unassignTag(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }

  // Private Networks
  async getPrivateNetworks(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async createPrivateNetwork(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async updatePrivateNetwork(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async deletePrivateNetwork(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async assignInstanceToNetwork(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async unassignInstanceFromNetwork(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }

  // VIPs (Virtual IPs)
  async getVips(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async createVip(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async updateVip(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async deleteVip(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }

  // Snapshots
  async getSnapshots(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async updateSnapshot(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async deleteSnapshot(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }
  async rollbackSnapshot(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }

  // Audits
  async getAudits(req, res) { res.status(501).json({ success: false, message: 'Not implemented yet' }); }

  /**
   * Execute VPS action (start, stop, restart, console)
   * POST /vps/instances/:instanceId/actions/:action
   */
  async executeVPSAction(req, res) {
    try {
      const { instanceId, action } = req.params;

      console.log(`🎯 VPS Action Request: ${action} on instance ${instanceId}`);
      console.log(`🔍 Received instanceId type: ${typeof instanceId}, value: "${instanceId}"`);
      console.log(`🔍 Received instanceId length: ${String(instanceId).length}`);

      // Check if instanceId looks like a MongoDB ObjectId
      if (/^[0-9a-fA-F]{24}$/.test(instanceId)) {
        console.log(`⚠️ WARNING: Received MongoDB ObjectId instead of Contabo instance ID: ${instanceId}`);
        return res.status(400).json({
          success: false,
          message: "vps.invalid_instance_id",
          error: "Invalid instance ID format. Expected Contabo instance ID (numeric), got MongoDB ObjectId."
        });
      }

      // Check if instanceId is numeric
      if (!/^\d+$/.test(String(instanceId).trim())) {
        console.log(`⚠️ WARNING: Received non-numeric instance ID: ${instanceId}`);
        return res.status(400).json({
          success: false,
          message: "vps.invalid_instance_id",
          error: `Invalid instance ID format. Expected numeric string, got: "${instanceId}"`
        });
      }

      // Valider l'action
      const validActions = ['start', 'stop', 'restart', 'console', 'cloud-init', 'reinstall', 'rescue'];
      if (!validActions.includes(action.toLowerCase())) {
        return res.status(400).json({
          success: false,
          message: req.t('vps.invalid_action'),
          error: `Invalid action: ${action}. Valid actions: ${validActions.join(', ')}`
        });
      }

      // Exécuter l'action
      const result = await this.vpsService.executeVPSAction(instanceId, action);

      console.log(`✅ VPS Action ${action} completed for instance ${instanceId}`);

      res.status(200).json({
        success: true,
        message: req.t('vps.action_executed_successfully'),
        action: action,
        instanceId: instanceId,
        data: result
      });

    } catch (error) {
      console.error(`❌ VPS Action failed:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.action_failed'),
        error: error.message
      });
    }
  }

  /**
   * Get VPS snapshots
   * GET /vps/instances/:instanceId/snapshots
   */
  async getVPSSnapshots(req, res) {
    try {
      const { instanceId } = req.params;

      console.log(`📸 Getting snapshots for VPS instance: ${instanceId}`);

      const snapshots = await this.vpsService.getVPSSnapshots(instanceId);

      console.log(`✅ Found ${snapshots.length} snapshots for instance ${instanceId}`);

      res.status(200).json({
        success: true,
        message: req.t('vps.snapshots_retrieved_successfully'),
        instanceId: instanceId,
        data: snapshots
      });

    } catch (error) {
      console.error(`❌ Failed to get VPS snapshots:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.snapshots_retrieval_failed'),
        error: error.message
      });
    }
  }

  /**
   * Create VPS snapshot
   * POST /vps/instances/:instanceId/snapshots
   */
  async createVPSSnapshot(req, res) {
    try {
      const { instanceId } = req.params;
      const { name, description } = req.body;

      console.log(`📸 Creating snapshot for VPS instance: ${instanceId}`);

      const snapshot = await this.vpsService.createVPSSnapshot(instanceId, name, description);

      console.log(`✅ Snapshot created successfully for instance ${instanceId}`);

      res.status(201).json({
        success: true,
        message: req.t('vps.snapshot_created_successfully'),
        instanceId: instanceId,
        data: snapshot
      });

    } catch (error) {
      console.error(`❌ Failed to create VPS snapshot:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.snapshot_creation_failed'),
        error: error.message
      });
    }
  }

  /**
   * Rename VPS snapshot
   * PUT /vps/instances/:instanceId/snapshots/:snapshotId/rename
   */
  async renameVPSSnapshot(req, res) {
    try {
      const { instanceId, snapshotId } = req.params;
      const { name, description } = req.body;

      console.log(`✏️ Renaming snapshot ${snapshotId} for VPS instance: ${instanceId}`);

      const snapshot = await this.vpsService.renameVPSSnapshot(instanceId, snapshotId, name, description);

      console.log(`✅ Snapshot renamed successfully`);

      res.status(200).json({
        success: true,
        message: req.t('vps.snapshot_renamed_successfully'),
        instanceId: instanceId,
        snapshotId: snapshotId,
        data: snapshot
      });

    } catch (error) {
      console.error(`❌ Failed to rename VPS snapshot:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.snapshot_rename_failed'),
        error: error.message
      });
    }
  }

  /**
   * Rollback VPS snapshot
   * POST /vps/instances/:instanceId/snapshots/:snapshotId/rollback
   */
  async rollbackVPSSnapshot(req, res) {
    try {
      const { instanceId, snapshotId } = req.params;

      console.log(`🔄 Rolling back snapshot ${snapshotId} for VPS instance: ${instanceId}`);

      const result = await this.vpsService.rollbackVPSSnapshot(instanceId, snapshotId);

      console.log(`✅ Snapshot rollback initiated successfully`);

      res.status(200).json({
        success: true,
        message: req.t('vps.snapshot_rollback_initiated'),
        instanceId: instanceId,
        snapshotId: snapshotId,
        data: result
      });

    } catch (error) {
      console.error(`❌ Failed to rollback VPS snapshot:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.snapshot_rollback_failed'),
        error: error.message
      });
    }
  }

  /**
   * Delete VPS snapshot
   * DELETE /vps/instances/:instanceId/snapshots/:snapshotId
   */
  async deleteVPSSnapshot(req, res) {
    try {
      const { instanceId, snapshotId } = req.params;

      console.log(`🗑️ Deleting snapshot ${snapshotId} for VPS instance: ${instanceId}`);

      const result = await this.vpsService.deleteVPSSnapshot(instanceId, snapshotId);

      console.log(`✅ Snapshot deleted successfully`);

      res.status(200).json({
        success: true,
        message: req.t('vps.snapshot_deleted_successfully'),
        instanceId: instanceId,
        snapshotId: snapshotId,
        data: result
      });

    } catch (error) {
      console.error(`❌ Failed to delete VPS snapshot:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.snapshot_deletion_failed'),
        error: error.message
      });
    }
  }

  /**
   * Check rescue status for VPS instance
   * GET /vps/instances/:instanceId/rescue-status
   */
  async checkRescueStatus(req, res) {
    try {
      const { instanceId } = req.params;

      console.log(`🔍 Checking rescue status for VPS instance: ${instanceId}`);

      const result = await this.vpsService.checkRescueStatus(instanceId);

      console.log(`✅ Rescue status check completed for instance ${instanceId}`);

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error(`❌ Failed to check rescue status:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.rescue_status_failed') || 'Failed to check rescue status',
        error: error.message
      });
    }
  }

  /**
   * Get available rescue system images
   * GET /vps/rescue-images
   */
  async getRescueImages(req, res) {
    try {
      console.log(`🛡️ Getting available rescue system images`);

      const result = await this.vpsService.getRescueImages();

      console.log(`✅ Retrieved rescue images successfully`);

      res.json({
        success: true,
        data: result.images
      });

    } catch (error) {
      console.error(`❌ Failed to get rescue images:`, error.message);
      res.status(500).json({
        success: false,
        message: req.t('vps.rescue_images_failed') || 'Failed to get rescue images',
        error: error.message
      });
    }
  }
}

// Export controller instance methods
const vpsController = new VPSController();

module.exports = {
  // Core VPS functionality
  getPlans: vpsController.getPlans.bind(vpsController),
  getPlanDetails: vpsController.getPlanDetails.bind(vpsController),
  createOrder: vpsController.createOrder.bind(vpsController),
  getUserOrders: vpsController.getUserOrders.bind(vpsController),
  getUserInstances: vpsController.getUserInstances.bind(vpsController),
  controlInstance: vpsController.controlInstance.bind(vpsController),
  getInstanceDetails: vpsController.getInstanceDetails.bind(vpsController),
  confirmPayment: vpsController.confirmPayment.bind(vpsController),
  getProviders: vpsController.getProviders.bind(vpsController),
  getInstanceStats: vpsController.getInstanceStats.bind(vpsController),
  createSnapshot: vpsController.createSnapshot.bind(vpsController),
  reinstallInstance: vpsController.reinstallInstance.bind(vpsController),
  getImages: vpsController.getImages.bind(vpsController),
  getSubOrderDetails: vpsController.getSubOrderDetails.bind(vpsController),

  // User management methods
  getProviderUsers: vpsController.getProviderUsers.bind(vpsController),
  getProviderUserDetails: vpsController.getProviderUserDetails.bind(vpsController),
  createProviderUser: vpsController.createProviderUser.bind(vpsController),
  updateProviderUser: vpsController.updateProviderUser.bind(vpsController),
  deleteProviderUser: vpsController.deleteProviderUser.bind(vpsController),
  generateUserClientSecret: vpsController.generateUserClientSecret.bind(vpsController),
  resetProviderUserPassword: vpsController.resetProviderUserPassword.bind(vpsController),

  // Data centers
  getDataCenters: vpsController.getDataCenters.bind(vpsController),
  getRegions: vpsController.getRegions.bind(vpsController),

  // Object storage
  getObjectStorages: vpsController.getObjectStorages.bind(vpsController),
  getObjectStorageDetails: vpsController.getObjectStorageDetails.bind(vpsController),
  createObjectStorage: vpsController.createObjectStorage.bind(vpsController),
  updateObjectStorage: vpsController.updateObjectStorage.bind(vpsController),
  cancelObjectStorage: vpsController.cancelObjectStorage.bind(vpsController),
  resizeObjectStorage: vpsController.resizeObjectStorage.bind(vpsController),
  getObjectStorageStats: vpsController.getObjectStorageStats.bind(vpsController),

  // Secrets management
  getSecrets: vpsController.getSecrets.bind(vpsController),
  createSecret: vpsController.createSecret.bind(vpsController),
  updateSecret: vpsController.updateSecret.bind(vpsController),
  deleteSecret: vpsController.deleteSecret.bind(vpsController),

  // Tags management
  getTags: vpsController.getTags.bind(vpsController),
  createTag: vpsController.createTag.bind(vpsController),
  updateTag: vpsController.updateTag.bind(vpsController),
  deleteTag: vpsController.deleteTag.bind(vpsController),
  assignTag: vpsController.assignTag.bind(vpsController),
  unassignTag: vpsController.unassignTag.bind(vpsController),

  // Private networks
  getPrivateNetworks: vpsController.getPrivateNetworks.bind(vpsController),
  createPrivateNetwork: vpsController.createPrivateNetwork.bind(vpsController),
  updatePrivateNetwork: vpsController.updatePrivateNetwork.bind(vpsController),
  deletePrivateNetwork: vpsController.deletePrivateNetwork.bind(vpsController),
  assignInstanceToNetwork: vpsController.assignInstanceToNetwork.bind(vpsController),
  unassignInstanceFromNetwork: vpsController.unassignInstanceFromNetwork.bind(vpsController),

  // VIPs (Virtual IPs)
  getVips: vpsController.getVips.bind(vpsController),
  createVip: vpsController.createVip.bind(vpsController),
  updateVip: vpsController.updateVip.bind(vpsController),
  deleteVip: vpsController.deleteVip.bind(vpsController),

  // Snapshots
  getSnapshots: vpsController.getSnapshots.bind(vpsController),
  updateSnapshot: vpsController.updateSnapshot.bind(vpsController),
  deleteSnapshot: vpsController.deleteSnapshot.bind(vpsController),
  rollbackSnapshot: vpsController.rollbackSnapshot.bind(vpsController),

  // Audits
  getAudits: vpsController.getAudits.bind(vpsController),

  // VPS Actions
  executeVPSAction: vpsController.executeVPSAction.bind(vpsController),
  checkRescueStatus: vpsController.checkRescueStatus.bind(vpsController),
  getRescueImages: vpsController.getRescueImages.bind(vpsController),

  // VPS Snapshots
  getVPSSnapshots: vpsController.getVPSSnapshots.bind(vpsController),
  createVPSSnapshot: vpsController.createVPSSnapshot.bind(vpsController),
  renameVPSSnapshot: vpsController.renameVPSSnapshot.bind(vpsController),
  rollbackVPSSnapshot: vpsController.rollbackVPSSnapshot.bind(vpsController),
  deleteVPSSnapshot: vpsController.deleteVPSSnapshot.bind(vpsController)
};
